# LimeTray Menu Transformation Test

This document explains how to test the LimeTray menu transformation from LimeTray format to unified menu structure.

## Test Payload Overview

The `limetray-menu-payload.json` file contains a comprehensive LimeTray menu payload that tests all major features:

### **1. Root Level Components**
- **Taxes**: 3 different tax types (GST 18%, GST 5%, Service Tax)
- **Charges**: 2 different charges (Delivery Charge, Packing Charge)
- **Categories**: 4 main categories with various complexity levels

### **2. Tax and Charge Testing**
```json
"taxes": [
  {
    "taxChargeId": "GST_18",
    "taxChargeName": "GST 18%",
    "taxValue": 18.0,
    "taxType": 1,           // Percentage tax
    "quantityWiseTax": true,
    "serviceId": "2",       // Delivery service
    "applicableOnItem": true
  },
  {
    "taxChargeId": "SERVICE_TAX",
    "taxValue": 25.0,
    "taxType": 0,           // Flat tax
    "quantityWiseTax": false,
    "applicableOnItem": false
  }
]
```

### **3. Category Hierarchy Testing**
- **Main Categories**: Appetizers, Main Course, Beverages
- **Subcategories**: Vegetarian Appetizers (nested under Appetizers)
- **Category Types**: VEG, NON-VEG, NONE
- **Localization**: English and Hindi names/descriptions

### **4. Product and SKU Testing**
- **Multiple SKUs per Product**: Chicken Wings (6 pieces, 12 pieces)
- **Different Product Types**: Veg (1), Non-Veg (2)
- **Price Variations**: Regular price, reduced price
- **Service-wise Pricing**: Different prices for Takeaway(1), Delivery(2), DineIn(3)
- **Stock Status**: In-stock and out-of-stock items

### **5. Addon Group Testing**
- **Nested Addon Groups**: Extra Sauces, Bread Options
- **Min/Max Constraints**: min: 0, max: 3 for sauces; min: 1, max: 2 for bread
- **Addon Products with SKUs**: BBQ Sauce, Plain Naan, Garlic Naan
- **Service-wise Addon Pricing**

### **6. Nutrition and Metadata Testing**
- **Nutrition Info**: Calories, protein, carbs, fat, allergens
- **Preparation Time**: Different cooking times
- **Serving Information**: Serves 1, 2, 4 people
- **Allergen Information**: gluten, soy, dairy, nuts

### **7. Localization Testing**
- **Multi-language Support**: English and Hindi
- **Localized Names**: Product and category names
- **Localized Descriptions**: Detailed descriptions in multiple languages

## How to Test the Transformation

### **1. Using cURL (Direct API Test)**
```bash
curl -X POST http://localhost:8081/api/v1/menu \
  -H "Content-Type: application/json" \
  -H "X-Consumer-Username: limetray" \
  -d @test-data/limetray-menu-payload.json
```

### **2. Expected Transformation Results**

#### **Categories Transformation**
- 4 main categories should be created
- 1 subcategory should be nested under Appetizers
- Category IDs should be prefixed with "LT" (LimeTray abbreviation)
- Food types should be mapped: VEG → "veg", NON-VEG → "non veg"

#### **Items Transformation**
- Each product SKU should become a separate item
- Item IDs should be prefixed with "LT"
- Prices should be taken from SKU level
- Food types should be mapped from productType

#### **Addon Groups Transformation**
- 2 addon groups should be created at root level
- Addon group IDs should be prefixed with "LT"
- Min/max constraints should be preserved
- Nested addon products should be flattened

#### **Taxes and Charges Transformation**
- Tax types should be mapped: 0 → "FIXED", 1 → "PERCENTAGE"
- Charge types should be mapped similarly
- IDs should be prefixed with "LT"

### **3. Validation Points**

#### **Data Integrity**
- All product SKUs should be transformed to items
- No data loss during transformation
- Proper ID prefixing with provider abbreviation
- Correct price mapping from SKU level

#### **Structure Validation**
- Addon groups moved to root level
- Hierarchical categories preserved
- Service-wise pricing handled correctly
- Localization data preserved

#### **Type Mapping**
- LimeTray service types (1,2,3) mapped to internal types
- Tax types (0,1) mapped to FIXED/PERCENTAGE
- Food types (1,2) mapped to veg/non-veg
- Category types mapped correctly

### **4. Testing Edge Cases**

The payload includes several edge cases:
- **Empty addon lists**: Some SKUs have empty addons
- **Missing optional fields**: Some products lack descriptions
- **Out of stock items**: Testing stock status handling
- **Service availability**: Some services marked as inactive
- **Nested structures**: Deep nesting of categories and addons

### **5. Expected Unified Menu Structure**

After transformation, you should see:
```json
{
  "categories": [
    {
      "id": "LT1001",
      "providerId": "1001",
      "name": "Appetizers",
      "items": [...],
      "subcategories": [...]
    }
  ],
  "addOnGroups": [
    {
      "id": "LT4001",
      "providerId": "4001",
      "name": "Extra Sauces",
      "addOns": [...]
    }
  ],
  "billComponents": {
    "taxes": [...],
    "charges": [...]
  }
}
```

## Debugging Tips

1. **Check Logs**: Look for transformation errors in application logs
2. **Validate JSON**: Ensure the payload is valid JSON
3. **Check Constants**: Verify LimeTray constants are properly defined
4. **Test Incrementally**: Start with simple payload and add complexity
5. **Compare with Other Providers**: Use UrbanPiper/Petpooja as reference

## Common Issues and Solutions

1. **Missing Provider Abbreviation**: Ensure "LT" is defined in constants
2. **Type Conversion Errors**: Check integer/string type handling
3. **Nil Pointer Errors**: Validate optional field handling
4. **ID Conflicts**: Ensure unique ID generation
5. **Localization Issues**: Handle missing localized fields gracefully
