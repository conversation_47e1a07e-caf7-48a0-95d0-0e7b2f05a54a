{"restaurant": {"restaurantId": "REST_LT_001", "restaurantName": "LimeTray Test Restaurant", "address": "123 Test Street, Test City", "phone": "+91-9876543210", "email": "<EMAIL>", "cuisine": ["Indian", "Continental"], "isActive": true}, "taxes": [{"taxChargeId": "GST_18", "taxChargeName": "GST 18%", "taxValue": 18.0, "taxType": 1, "quantityWiseTax": true, "serviceId": "2", "applicableOnItem": true, "chargeTaxes": []}, {"taxChargeId": "GST_5", "taxChargeName": "GST 5%", "taxValue": 5.0, "taxType": 1, "quantityWiseTax": true, "serviceId": "1", "applicableOnItem": true, "chargeTaxes": []}, {"taxChargeId": "SERVICE_TAX", "taxChargeName": "Service Tax", "taxValue": 25.0, "taxType": 0, "quantityWiseTax": false, "serviceId": "3", "applicableOnItem": false, "chargeTaxes": []}], "charges": [{"taxChargeId": "DELIVERY_CHARGE", "taxChargeName": "Delivery Charge", "taxValue": 50.0, "taxType": 0, "quantityWiseTax": false, "serviceId": "2", "applicableOnItem": false, "chargeTaxes": ["GST_18"]}, {"taxChargeId": "PACKING_CHARGE", "taxChargeName": "Packing Charge", "taxValue": 5.0, "taxType": 1, "quantityWiseTax": true, "serviceId": "1", "applicableOnItem": true, "chargeTaxes": ["GST_5"]}], "categories": [{"categoryId": 1001, "categoryName": "Appetizers", "categoryNameLocalized": {"en": "Appetizers", "hi": "स्टार्टर्स"}, "categoryDescription": "Delicious starters to begin your meal", "categoryDescriptionLocalized": {"en": "Delicious starters to begin your meal", "hi": "आपके भोजन की शुरुआत के लिए स्वादिष्ट स्टार्टर्स"}, "rank": 1, "productList": [{"productId": 2001, "productType": "2", "rank": 1, "productName": "Chicken Wings", "productNameLocalized": {"en": "Chicken Wings", "hi": "चिकन विंग्स"}, "productImageList": ["https://example.com/images/chicken-wings.jpg"], "productDescription": "Spicy grilled chicken wings", "productDescriptionLocalized": {"en": "Spicy grilled chicken wings", "hi": "मसालेदार ग्रिल्ड चिकन विंग्स"}, "productSkuList": [{"productSkuId": 3001, "outOfStock": false, "productSkuName": "6 Pieces", "productSkuNameLocalized": {"en": "6 Pieces", "hi": "6 टुकड़े"}, "productSkuDescription": "Half dozen chicken wings", "productSKUDescriptionLocalized": {"en": "Half dozen chicken wings", "hi": "आधा दर्जन चिकन विंग्स"}, "productSkuImageList": ["https://example.com/images/chicken-wings-6.jpg"], "productSkuPrice": 299.0, "reducedPrice": 249.0, "allergens": ["gluten", "soy"], "productSKUInfoDTO": {"calories": 450, "preparationTime": 15, "serves": 2, "allergy": "gluten, soy", "servingSize": "300g", "protein": "35g", "carbs": "5g", "fiber": "1g", "fat": "25g", "saturatedFat": "8g", "sodium": "800mg", "cholesterol": "120mg"}, "addons": [{"categoryId": 4001, "categoryName": "Extra Sauces", "categoryNameLocalized": {"en": "Extra Sauces", "hi": "अतिरिक्त सॉस"}, "categoryDescription": "Choose your favorite sauce", "categoryDescriptionLocalized": {"en": "Choose your favorite sauce", "hi": "अपनी पसंदीदा सॉस चुनें"}, "rank": 1, "productList": [{"productId": 5001, "productType": "1", "rank": 1, "productName": "BBQ Sauce", "productNameLocalized": {"en": "BBQ Sauce", "hi": "बीबीक्यू सॉस"}, "productImageList": [], "productDescription": "Tangy BBQ sauce", "productSkuList": [{"productSkuId": 6001, "outOfStock": false, "productSkuName": "Regular", "productSkuPrice": 25.0, "allergens": [], "addons": [], "services": [{"serviceId": "1", "price": 25.0, "isActive": true}, {"serviceId": "2", "price": 25.0, "isActive": true}]}], "productTaxes": ["GST_5"], "productCharges": [], "productTagList": ["sauce", "vegetarian"]}], "categoryImageList": [], "categoryType": "VEG", "min": 0, "max": 3, "isActive": true, "isComboEDV": false}], "services": [{"serviceId": "1", "price": 299.0, "isActive": true}, {"serviceId": "2", "price": 319.0, "isActive": true}, {"serviceId": "3", "price": 279.0, "isActive": true}]}, {"productSkuId": 3002, "outOfStock": false, "productSkuName": "12 Pieces", "productSkuNameLocalized": {"en": "12 Pieces", "hi": "12 टुकड़े"}, "productSkuDescription": "Full dozen chicken wings", "productSkuPrice": 549.0, "reducedPrice": 499.0, "allergens": ["gluten", "soy"], "productSKUInfoDTO": {"calories": 900, "preparationTime": 20, "serves": 4, "protein": "70g", "fat": "50g"}, "addons": [{"categoryId": 4001, "categoryName": "Extra Sauces", "rank": 1, "productList": [], "categoryType": "VEG", "min": 0, "max": 5, "isActive": true, "isComboEDV": false}], "services": [{"serviceId": "1", "price": 549.0, "isActive": true}, {"serviceId": "2", "price": 569.0, "isActive": true}]}], "productTaxes": ["GST_18"], "productCharges": ["PACKING_CHARGE"], "productTagList": ["non-veg", "spicy", "grilled"], "productSlots": [{"brandSlotId": 7001, "brandSlotName": "Lunch Special", "brandSlotTime": [{"brandSlotTimeId": 8001, "startTime": "12:00", "endTime": "15:00"}], "brandSlotDate": [{"brandSlotDateId": 9001, "startDate": 20240101, "endDate": 20241231}], "weekDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]}]}], "categoryImageList": ["https://example.com/images/appetizers-category.jpg"], "categoryType": "NON-VEG", "childCategories": [{"categoryId": 1002, "categoryName": "Vegetarian Appetizers", "categoryNameLocalized": {"en": "Vegetarian Appetizers", "hi": "शाकाहारी स्टार्टर्स"}, "categoryDescription": "Delicious vegetarian starters", "rank": 1, "productList": [{"productId": 2002, "productType": "1", "rank": 1, "productName": "<PERSON><PERSON>", "productNameLocalized": {"en": "<PERSON><PERSON>", "hi": "पनीर टिक्का"}, "productImageList": ["https://example.com/images/paneer-tikka.jpg"], "productDescription": "Grilled cottage cheese cubes", "productSkuList": [{"productSkuId": 3003, "outOfStock": false, "productSkuName": "Regular", "productSkuPrice": 249.0, "allergens": ["dairy"], "addons": [], "services": [{"serviceId": "1", "price": 249.0, "isActive": true}, {"serviceId": "2", "price": 269.0, "isActive": true}, {"serviceId": "3", "price": 229.0, "isActive": true}]}], "productTaxes": ["GST_5"], "productCharges": [], "productTagList": ["vegetarian", "grilled", "popular"]}], "categoryImageList": ["https://example.com/images/veg-appetizers.jpg"], "categoryType": "VEG", "childCategories": [], "serviceIds": ["1", "2", "3"], "isComboEDV": false}], "serviceIds": ["1", "2", "3"], "isComboEDV": false}, {"categoryId": 1003, "categoryName": "Main Course", "categoryNameLocalized": {"en": "Main Course", "hi": "मुख्य व्यंजन"}, "categoryDescription": "Hearty main dishes", "rank": 2, "productList": [{"productId": 2003, "productType": "2", "rank": 1, "productName": "Butter Chicken", "productNameLocalized": {"en": "Butter Chicken", "hi": "बटर चिकन"}, "productImageList": ["https://example.com/images/butter-chicken.jpg"], "productDescription": "Creamy tomato-based chicken curry", "productSkuList": [{"productSkuId": 3004, "outOfStock": false, "productSkuName": "Half Portion", "productSkuPrice": 349.0, "allergens": ["dairy", "nuts"], "productSKUInfoDTO": {"calories": 520, "preparationTime": 25, "serves": 1, "protein": "28g", "carbs": "12g", "fat": "38g"}, "addons": [{"categoryId": 4002, "categoryName": "Bread Options", "categoryDescription": "Choose your bread", "rank": 1, "productList": [{"productId": 5002, "productType": "1", "productName": "<PERSON><PERSON>", "productSkuList": [{"productSkuId": 6002, "outOfStock": false, "productSkuName": "Plain Naan", "productSkuPrice": 45.0, "services": [{"serviceId": "1", "price": 45.0, "isActive": true}]}, {"productSkuId": 6003, "outOfStock": false, "productSkuName": "<PERSON><PERSON><PERSON>", "productSkuPrice": 55.0, "services": [{"serviceId": "1", "price": 55.0, "isActive": true}]}]}], "categoryType": "VEG", "min": 1, "max": 2, "isActive": true, "isComboEDV": false}], "services": [{"serviceId": "1", "price": 349.0, "isActive": true}, {"serviceId": "2", "price": 369.0, "isActive": true}, {"serviceId": "3", "price": 329.0, "isActive": true}]}, {"productSkuId": 3005, "outOfStock": false, "productSkuName": "Full Portion", "productSkuPrice": 549.0, "allergens": ["dairy", "nuts"], "productSKUInfoDTO": {"calories": 1040, "preparationTime": 25, "serves": 2, "protein": "56g", "carbs": "24g", "fat": "76g"}, "addons": [{"categoryId": 4002, "categoryName": "Bread Options", "rank": 1, "productList": [], "categoryType": "VEG", "min": 1, "max": 4, "isActive": true, "isComboEDV": false}], "services": [{"serviceId": "1", "price": 549.0, "isActive": true}, {"serviceId": "2", "price": 569.0, "isActive": true}, {"serviceId": "3", "price": 529.0, "isActive": false}]}], "productTaxes": ["GST_5"], "productCharges": ["PACKING_CHARGE"], "productTagList": ["non-veg", "curry", "popular", "creamy"]}], "categoryImageList": ["https://example.com/images/main-course.jpg"], "categoryType": "NON-VEG", "childCategories": [], "serviceIds": ["1", "2", "3"], "isComboEDV": false}, {"categoryId": 1004, "categoryName": "Beverages", "categoryDescription": "Refreshing drinks", "rank": 3, "productList": [{"productId": 2004, "productType": "1", "rank": 1, "productName": "Fresh Lime Soda", "productImageList": ["https://example.com/images/lime-soda.jpg"], "productDescription": "Refreshing lime soda with mint", "productSkuList": [{"productSkuId": 3006, "outOfStock": false, "productSkuName": "Regular", "productSkuPrice": 89.0, "allergens": [], "productSKUInfoDTO": {"calories": 120, "preparationTime": 5, "serves": 1, "carbs": "30g", "sodium": "15mg"}, "addons": [], "services": [{"serviceId": "1", "price": 89.0, "isActive": true}, {"serviceId": "2", "price": 89.0, "isActive": true}, {"serviceId": "3", "price": 79.0, "isActive": true}]}], "productTaxes": ["GST_18"], "productCharges": [], "productTagList": ["beverage", "refreshing", "vegetarian"]}], "categoryImageList": ["https://example.com/images/beverages.jpg"], "categoryType": "VEG", "childCategories": [], "serviceIds": ["1", "2", "3"], "isComboEDV": false}]}