package limetray

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// TransformOrder transforms unified order to LimeTray order format
func TransformOrder(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to LimeTray format using struct-based approach
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.LimeTrayClientId)

	return transformedOrder
}

// TransformRider transforms unified rider to LimeTray rider format
func TransformRider(rider orders.Rider) interface{} {
	// TODO: Implement LimeTray rider transformation
	return rider
}
