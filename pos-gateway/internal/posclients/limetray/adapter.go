package limetray

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// LimeTrayAdapter implements the POSAdapter interface for LimeTray
type LimeTrayAdapter struct {
	client *Client
}

// NewLimeTrayAdapter creates a new LimeTray adapter instance
func NewLimeTrayAdapter(client *Client) adapter.POSAdapter {
	return &LimeTrayAdapter{
		client: client,
	}
}

// SendOrder sends order details to LimeTray
func (l *LimeTrayAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	transformedOrder := TransformOrder(order)
	_, err := l.client.SendOrder(transformedOrder)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to LimeTray successfully",
	}, nil
}

// SendRiderDetails sends rider details to LimeTray
func (l *LimeTrayAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	transformedRider := TransformRider(rider)
	_, err := l.client.SendRiderDetails(transformedRider)
	return types.SuccessResponse{}, err
}

// SendMenuProcessingRequestStatus sends menu processing status to LimeTray
func (l *LimeTrayAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, callbackURL string) (types.SuccessResponse, error) {
	_, err := l.client.SendMenuProcessingRequestStatus(status, callbackURL)
	return types.SuccessResponse{}, err
}

// TransformOrder transforms unified order to LimeTray order format using centralized transformation
func TransformOrder(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to LimeTray format using struct-based approach
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.LimeTrayClientId)

	return transformedOrder
}

// TransformRider transforms unified rider to LimeTray rider format
func TransformRider(rider orders.Rider) interface{} {
	// TODO: Implement LimeTray rider transformation
	return rider
}
