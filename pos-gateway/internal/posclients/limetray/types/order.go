package limetray

// SaveOrderRequest represents the LimeTray order request structure
type SaveOrderRequest struct {
	OrderID      string       `json:"orderId"`
	RestaurantID string       `json:"restaurantId"`
	Customer     Customer     `json:"customer"`
	Items        []OrderItem  `json:"items"`
	Payment      Payment      `json:"payment"`
	OrderInfo    OrderInfo    `json:"orderInfo"`
	ServiceType  string       `json:"serviceType"` // 1=Takeaway, 2=Delivery, 3=DineIn
}

// Customer represents customer information
type Customer struct {
	Name        string  `json:"name"`
	Phone       string  `json:"phone"`
	Email       string  `json:"email"`
	Address     Address `json:"address"`
}

// Address represents customer address
type Address struct {
	Line1     string  `json:"line1"`
	Line2     string  `json:"line2"`
	City      string  `json:"city"`
	State     string  `json:"state"`
	Pincode   string  `json:"pincode"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// OrderItem represents individual order item
type OrderItem struct {
	ProductID    string        `json:"productId"`
	ProductSkuID string        `json:"productSkuId"`
	Name         string        `json:"name"`
	Quantity     int           `json:"quantity"`
	Price        float64       `json:"price"`
	Total        float64       `json:"total"`
	Addons       []OrderAddon  `json:"addons"`
	Taxes        []OrderTax    `json:"taxes"`
	Charges      []OrderCharge `json:"charges"`
}

// OrderAddon represents addon items
type OrderAddon struct {
	ProductID    string  `json:"productId"`
	ProductSkuID string  `json:"productSkuId"`
	Name         string  `json:"name"`
	Price        float64 `json:"price"`
	Quantity     int     `json:"quantity"`
}

// OrderTax represents tax information
type OrderTax struct {
	TaxID    string  `json:"taxId"`
	Name     string  `json:"name"`
	Value    float64 `json:"value"`
	Type     int     `json:"type"` // 0=Flat, 1=Percentage
	Amount   float64 `json:"amount"`
}

// OrderCharge represents charge information
type OrderCharge struct {
	ChargeID string  `json:"chargeId"`
	Name     string  `json:"name"`
	Value    float64 `json:"value"`
	Type     int     `json:"type"` // 0=Flat, 1=Percentage
	Amount   float64 `json:"amount"`
}

// Payment represents payment information
type Payment struct {
	Mode          string  `json:"mode"`          // cash, online, card
	Status        string  `json:"status"`        // paid, pending
	Amount        float64 `json:"amount"`
	TransactionID string  `json:"transactionId"`
}

// OrderInfo represents order metadata
type OrderInfo struct {
	OrderDate        string  `json:"orderDate"`
	DeliveryDate     string  `json:"deliveryDate"`
	DeliveryTime     string  `json:"deliveryTime"`
	Instructions     string  `json:"instructions"`
	SubTotal         float64 `json:"subTotal"`
	TotalTax         float64 `json:"totalTax"`
	TotalCharges     float64 `json:"totalCharges"`
	TotalDiscount    float64 `json:"totalDiscount"`
	GrandTotal       float64 `json:"grandTotal"`
	PreparationTime  int     `json:"preparationTime"`
}
