package limetray

// Menu represents the LimeTray menu response structure
type Menu struct {
	Restaurant *Restaurant        `json:"restaurant,omitempty"`
	Taxes      []ProductTaxCharge `json:"taxes"`
	Charges    []ProductTaxCharge `json:"charges"`
	Categories []MenuCategory     `json:"categories"`
}

// Restaurant represents restaurant information
type Restaurant struct {
	RestaurantID   string   `json:"restaurantId"`
	RestaurantName string   `json:"restaurantName"`
	Address        string   `json:"address"`
	Phone          string   `json:"phone"`
	Email          string   `json:"email"`
	Cuisine        []string `json:"cuisine"`
	IsActive       bool     `json:"isActive"`
}

// ProductTaxCharge represents taxes and charges for items
type ProductTaxCharge struct {
	TaxChargeID      string   `json:"taxChargeId"`
	TaxChargeName    string   `json:"taxChargeName"`
	TaxValue         float64  `json:"taxValue"`
	TaxType          int      `json:"taxType"`          // 0 for Flat tax, 1 for Percentage tax
	QuantityWiseTax  bool     `json:"quantityWiseTax"`  // is tax going to be multiplied with item
	ServiceID        string   `json:"serviceId"`        // Id of the service(Takeaway(1), Delivery(2), DineIn(3))
	ApplicableOnItem bool     `json:"applicableOnItem"` // Is the charge applicable for item/cart
	ChargeTaxes      []string `json:"chargeTaxes"`      // List of taxes applied on charge
}

// MenuCategory represents a menu category
type MenuCategory struct {
	CategoryID                   int                   `json:"categoryId"`
	CategoryName                 string                `json:"categoryName"`
	CategoryNameLocalized        *LocalizedName        `json:"categoryNameLocalized,omitempty"`
	CategoryDescription          *string               `json:"categoryDescription,omitempty"`
	CategoryDescriptionLocalized *LocalizedDescription `json:"categoryDescriptionLocalized,omitempty"`
	Rank                         int                   `json:"rank"`
	ProductList                  []MenuProduct         `json:"productList"`
	CategoryImageList            []string              `json:"categoryImageList"`
	CategoryType                 string                `json:"categoryType"` // Enum: NON-VEG,VEG,NONE
	ChildCategories              []MenuCategory        `json:"childCategories"`
	ServiceIDs                   []string              `json:"serviceIds"` // List of service Ids for which category is active
	IsComboEDV                   bool                  `json:"isComboEDV"` // Is category a combo
}

// MenuProduct represents a menu product
type MenuProduct struct {
	ProductID                   *int                  `json:"productId,omitempty"`
	ProductType                 *string               `json:"productType,omitempty"` // 1 for Veg, 2 for NonVeg
	Rank                        *int                  `json:"rank,omitempty"`
	ProductName                 *string               `json:"productName,omitempty"`
	ProductNameLocalized        *LocalizedName        `json:"productNameLocalized,omitempty"`
	ProductImageList            []string              `json:"productImageList"`
	ProductDescription          *string               `json:"productDescription,omitempty"`
	ProductDescriptionLocalized *LocalizedDescription `json:"productDescriptionLocalized,omitempty"`
	ProductSkuList              []MenuProductSKU      `json:"productSkuList"`
	ProductTaxes                []string              `json:"productTaxes"`
	ProductCharges              []string              `json:"productCharges"`
	ProductTagList              []string              `json:"productTagList"`
	ProductSlots                []MenuProductSlots    `json:"productSlots"`
}

// MenuProductSKU represents a product variant/SKU
type MenuProductSKU struct {
	ProductSkuID                   *int                   `json:"productSkuId,omitempty"`
	OutOfStock                     bool                   `json:"outOfStock"`
	ProductSkuName                 *string                `json:"productSkuName,omitempty"`
	ProductSkuNameLocalized        *LocalizedName         `json:"productSkuNameLocalized,omitempty"`
	ProductSkuDescription          *string                `json:"productSkuDescription,omitempty"`
	ProductSKUDescriptionLocalized *LocalizedDescription  `json:"productSKUDescriptionLocalized,omitempty"`
	ProductSkuImageList            []string               `json:"productSkuImageList"`
	ProductSkuPrice                *float64               `json:"productSkuPrice,omitempty"`
	ReducedPrice                   *float64               `json:"reducedPrice,omitempty"`
	Allergens                      []string               `json:"allergens"`
	ProductSKUInfoDTO              *ProductSKUInfo        `json:"productSKUInfoDTO,omitempty"`
	Addons                         []ProductAddonTemplate `json:"addons"`
	Services                       []ProductSKUService    `json:"services"`
}

// ProductSKUInfo represents nutrition info for SKU
type ProductSKUInfo struct {
	Calories        *float64 `json:"calories,omitempty"`
	PreparationTime *int     `json:"preparationTime,omitempty"`
	Serves          *int     `json:"serves,omitempty"`
	Allergy         *string  `json:"allergy,omitempty"`
	ServingSize     *string  `json:"servingSize,omitempty"`
	Protein         *string  `json:"protein,omitempty"`
	Carbs           *string  `json:"carbs,omitempty"`
	Fiber           *string  `json:"fiber,omitempty"`
	Polydextrose    *string  `json:"polydextrose,omitempty"`
	Caffeine        *string  `json:"caffeine,omitempty"`
	Sweetener       *string  `json:"sweetener,omitempty"`
	MSG             *string  `json:"msg,omitempty"`
	Fat             *string  `json:"fat,omitempty"`
	SaturatedFat    *string  `json:"saturatedFat,omitempty"`
	TransFat        *string  `json:"transFat,omitempty"`
	Sodium          *string  `json:"sodium,omitempty"`
	Cholesterol     *string  `json:"cholesterol,omitempty"`
	Polyols         *string  `json:"polyols,omitempty"`
	Salt            *string  `json:"salt,omitempty"`
	TotalSugar      *string  `json:"totalsugar,omitempty"`
	AddedSugar      *string  `json:"addedsugar,omitempty"`
}

// ProductAddonTemplate represents addon groups
type ProductAddonTemplate struct {
	CategoryID                   *int                  `json:"categoryId"`
	CategoryName                 *string               `json:"categoryName"`
	CategoryNameLocalized        *LocalizedName        `json:"categoryNameLocalized,omitempty"`
	CategoryDescription          string                `json:"categoryDescription"`
	CategoryDescriptionLocalized *LocalizedDescription `json:"categoryDescriptionLocalized,omitempty"`
	Rank                         int                   `json:"rank"`
	ProductList                  []MenuProduct         `json:"productList"`
	CategoryImageList            []string              `json:"categoryImageList"`
	CategoryType                 string                `json:"categoryType"` // Enum: NON-VEG,VEG,NONE
	Min                          *int                  `json:"min,omitempty"`
	Max                          *int                  `json:"max,omitempty"`
	IsActive                     *bool                 `json:"isActive,omitempty"`
	IsComboEDV                   bool                  `json:"isComboEDV"`
}

// ProductSKUService represents service wise price and availability
type ProductSKUService struct {
	ServiceID string   `json:"serviceId"` // Id of the service(Takeaway(1), Delivery(2), DineIn(3))
	Price     *float64 `json:"price,omitempty"`
	IsActive  bool     `json:"isActive"`
}

// MenuProductSlots represents slots for products
type MenuProductSlots struct {
	BrandSlotID   int             `json:"brandSlotId"`
	BrandSlotName string          `json:"brandSlotName"`
	BrandSlotTime []BrandSlotTime `json:"brandSlotTime"`
	BrandSlotDate []BrandSlotDate `json:"brandSlotDate"`
	WeekDays      []string        `json:"weekDays"`
}

// BrandSlotTime represents time slots
type BrandSlotTime struct {
	BrandSlotTimeID int    `json:"brandSlotTimeId"`
	StartTime       string `json:"startTime"`
	EndTime         string `json:"endTime"`
}

// BrandSlotDate represents date slots
type BrandSlotDate struct {
	BrandSlotDateID int `json:"brandSlotDateId"`
	StartDate       int `json:"startDate"`
	EndDate         int `json:"endDate"`
}

// LocalizedName represents language specific names
type LocalizedName map[string]string

// LocalizedDescription represents language specific descriptions
type LocalizedDescription map[string]string
