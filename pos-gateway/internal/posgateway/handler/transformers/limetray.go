package transformers

import (
	"strconv"
	"strings"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/limetray"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
)

// TransformLimeTrayMenu converts LimeTray menu format to common format
func (t *transformersImpl) TransformLimeTrayMenu(menu *map[string]interface{}) *common.UnifiedMenu {
	var limeTrayMenu limetray.Menu
	err := utils.UnmarshalJSONToInterface(menu, &limeTrayMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling LimeTray menu",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}

	if len(limeTrayMenu.Categories) == 0 {
		return nil
	}

	// Transform categories
	categories := transformLimeTrayCategories(limeTrayMenu.Categories)

	// Transform taxes
	taxes := transformLimeTrayTaxes(limeTrayMenu.Taxes)

	// Transform charges
	charges := transformLimeTrayCharges(limeTrayMenu.Charges)

	// Collect all addon groups from categories and items
	addonGroupsMap := make(map[string]limetray.ProductAddonTemplate)
	collectLimeTrayAddonGroups(limeTrayMenu.Categories, addonGroupsMap)

	// Transform addon groups to root level
	addonGroups := transformLimeTrayAddOnGroupsToRoot(addonGroupsMap)

	unifiedMenu := &common.UnifiedMenu{
		Categories:    categories,
		AddOnGroups:   addonGroups,
		VariantGroups: []common.VariantGroup{}, // LimeTray uses SKUs instead of variants
		Variants:      []common.Variant{},      // LimeTray uses SKUs instead of variants
		BillComponents: &common.BillComponents{
			Charges: charges,
			Taxes:   taxes,
		},
	}

	return unifiedMenu
}

// transformLimeTrayCategories converts LimeTray categories to common format
func transformLimeTrayCategories(categories []limetray.MenuCategory) []common.Category {
	if len(categories) == 0 {
		return nil
	}

	commonCategories := make([]common.Category, 0, len(categories))
	for _, category := range categories {
		providerId := strconv.Itoa(category.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonCategory := common.Category{
			ID:            &id,
			ProviderId:    &providerId,
			Name:          &category.CategoryName,
			Description:   category.CategoryDescription,
			ImageURL:      getFirstImageURL(category.CategoryImageList),
			SortOrder:     category.Rank,
			Items:         transformLimeTrayItems(category.ProductList),
			Subcategories: transformLimeTraySubcategories(category.ChildCategories),
		}
		commonCategories = append(commonCategories, commonCategory)
	}

	return commonCategories
}

// transformLimeTraySubcategories converts LimeTray subcategories to common format
func transformLimeTraySubcategories(subcategories []limetray.MenuCategory) []common.Category {
	if len(subcategories) == 0 {
		return nil
	}

	commonSubcategories := make([]common.Category, 0, len(subcategories))
	for _, subcategory := range subcategories {
		providerId := strconv.Itoa(subcategory.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonSubcategory := common.Category{
			ID:            &id,
			ProviderId:    &providerId,
			Name:          &subcategory.CategoryName,
			Description:   subcategory.CategoryDescription,
			ImageURL:      getFirstImageURL(subcategory.CategoryImageList),
			SortOrder:     subcategory.Rank,
			Items:         transformLimeTrayItems(subcategory.ProductList),
			Subcategories: transformLimeTraySubcategories(subcategory.ChildCategories),
		}
		commonSubcategories = append(commonSubcategories, commonSubcategory)
	}

	return commonSubcategories
}

// transformLimeTrayItems converts LimeTray products to common items
func transformLimeTrayItems(products []limetray.MenuProduct) []common.Item {
	if len(products) == 0 {
		return nil
	}

	commonItems := make([]common.Item, 0)
	for _, product := range products {
		if product.ProductID == nil {
			continue
		}

		// For each product, create items from its SKUs
		for _, sku := range product.ProductSkuList {
			if sku.ProductSkuID == nil {
				continue
			}

			providerId := strconv.Itoa(*sku.ProductSkuID)
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			// Transform food type
			foodType := "not Specified" // Default value
			if product.ProductType != nil {
				if internalType, ok := constants.LimeTrayFoodTypesMapToInternal[*product.ProductType]; ok {
					foodType = internalType
				}
			}

			// Get price from SKU
			var price *float64
			if sku.ProductSkuPrice != nil {
				price = sku.ProductSkuPrice
			}

			// Transform addon group IDs
			addOnGroupIDs := make([]string, 0)
			for _, addon := range sku.Addons {
				if addon.CategoryID != nil {
					addonProviderId := strconv.Itoa(*addon.CategoryID)
					addonId := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + addonProviderId
					addOnGroupIDs = append(addOnGroupIDs, addonId)
				}
			}

			// Get item name (prefer SKU name, fallback to product name)
			var itemName *string
			if sku.ProductSkuName != nil && *sku.ProductSkuName != "" {
				itemName = sku.ProductSkuName
			} else if product.ProductName != nil {
				itemName = product.ProductName
			}

			// Get item description (prefer SKU description, fallback to product description)
			var itemDescription *string
			if sku.ProductSkuDescription != nil && *sku.ProductSkuDescription != "" {
				itemDescription = sku.ProductSkuDescription
			} else if product.ProductDescription != nil {
				itemDescription = product.ProductDescription
			}

			// Get image URL (prefer SKU images, fallback to product images)
			var imageURL *string
			if len(sku.ProductSkuImageList) > 0 {
				imageURL = &sku.ProductSkuImageList[0]
			} else if len(product.ProductImageList) > 0 {
				imageURL = &product.ProductImageList[0]
			}

			commonItem := common.Item{
				ID:            &id,
				ProviderId:    &providerId,
				Name:          itemName,
				Description:   itemDescription,
				ImageURL:      imageURL,
				Price:         price,
				InStock:       &[]bool{!sku.OutOfStock}[0],
				FoodType:      &foodType,
				AddOnGroupIDs: addOnGroupIDs,
				SortOrder:     getProductRank(product.Rank),
			}

			commonItems = append(commonItems, commonItem)
		}
	}

	return commonItems
}

// Helper functions
func transformLimeTrayCategoryTypeToFoodType(categoryType string) string {
	switch strings.ToUpper(categoryType) {
	case constants.LimeTrayCategoryTypeVeg:
		return "veg"
	case constants.LimeTrayCategoryTypeNonVeg:
		return "non veg"
	default:
		return "not Specified"
	}
}

func getFirstImageURL(images []string) *string {
	if len(images) > 0 {
		return &images[0]
	}
	return nil
}

func getProductRank(rank *int) int {
	if rank != nil {
		return *rank
	}
	return 0
}

// transformLimeTrayTaxes converts LimeTray taxes to common format
func transformLimeTrayTaxes(taxes []limetray.ProductTaxCharge) []common.Tax {
	if len(taxes) == 0 {
		return nil
	}

	commonTaxes := make([]common.Tax, 0)
	for _, tax := range taxes {
		providerId := tax.TaxChargeID
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonTax := common.Tax{
			ID:         &id,
			ProviderId: &providerId,
			Name:       &tax.TaxChargeName,
			Value:      &tax.TaxValue,
		}
		commonTaxes = append(commonTaxes, commonTax)
	}

	return commonTaxes
}

// transformLimeTrayCharges converts LimeTray charges to common format
func transformLimeTrayCharges(charges []limetray.ProductTaxCharge) []common.Charge {
	if len(charges) == 0 {
		return nil
	}

	commonCharges := make([]common.Charge, 0)
	for _, charge := range charges {
		providerId := charge.TaxChargeID
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		// Transform charge type
		var chargeType string
		if charge.TaxType == constants.LimeTrayTaxTypeFlat {
			chargeType = "FIXED"
		} else {
			chargeType = "PERCENTAGE"
		}

		commonCharge := common.Charge{
			ID:         &id,
			ProviderId: &providerId,
			Name:       &charge.TaxChargeName,
			Value:      &charge.TaxValue,
			Type:       &chargeType,
		}
		commonCharges = append(commonCharges, commonCharge)
	}

	return commonCharges
}

// collectLimeTrayAddonGroups recursively collects all addon groups from categories and items
func collectLimeTrayAddonGroups(categories []limetray.MenuCategory, addonGroupsMap map[string]limetray.ProductAddonTemplate) {
	for _, category := range categories {
		// Process items in current category
		for _, product := range category.ProductList {
			// Process SKUs in product
			for _, sku := range product.ProductSkuList {
				// Collect addon groups from SKU
				for _, addon := range sku.Addons {
					if addon.CategoryID != nil {
						addonKey := strconv.Itoa(*addon.CategoryID)
						addonGroupsMap[addonKey] = addon
					}
				}
			}
		}

		// Recursively process subcategories
		collectLimeTrayAddonGroups(category.ChildCategories, addonGroupsMap)
	}
}

// transformLimeTrayAddOnGroupsToRoot transforms addon groups to root level
func transformLimeTrayAddOnGroupsToRoot(groupsMap map[string]limetray.ProductAddonTemplate) []common.AddOnGroup {
	commonGroups := make([]common.AddOnGroup, 0)

	for _, group := range groupsMap {
		if group.CategoryID == nil {
			continue
		}

		providerId := strconv.Itoa(*group.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonGroup := common.AddOnGroup{
			ID:             &id,
			ProviderId:     &providerId,
			Name:           group.CategoryName,
			MinimumNeeded:  group.Min,
			MaximumAllowed: group.Max,
			AddOns:         transformLimeTrayAddOns(group.ProductList),
			SortOrder:      group.Rank,
		}
		commonGroups = append(commonGroups, commonGroup)
	}

	return commonGroups
}

// transformLimeTrayAddOns converts LimeTray addon products to common addons
func transformLimeTrayAddOns(products []limetray.MenuProduct) []common.AddOn {
	if len(products) == 0 {
		return nil
	}

	commonAddOns := make([]common.AddOn, 0)
	for _, product := range products {
		if product.ProductID == nil {
			continue
		}

		// For each product, create addons from its SKUs
		for _, sku := range product.ProductSkuList {
			if sku.ProductSkuID == nil {
				continue
			}

			providerId := strconv.Itoa(*sku.ProductSkuID)
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			// Get addon name (prefer SKU name, fallback to product name)
			var addonName *string
			if sku.ProductSkuName != nil && *sku.ProductSkuName != "" {
				addonName = sku.ProductSkuName
			} else if product.ProductName != nil {
				addonName = product.ProductName
			}

			// Get price from SKU
			var price *float64
			if sku.ProductSkuPrice != nil {
				price = sku.ProductSkuPrice
			}

			commonAddOn := common.AddOn{
				ID:         &id,
				ProviderId: &providerId,
				Name:       addonName,
				Price:      price,
				InStock:    &[]bool{!sku.OutOfStock}[0],
				SortOrder:  getProductRank(product.Rank),
			}

			commonAddOns = append(commonAddOns, commonAddOn)
		}
	}

	return commonAddOns
}

// TransformUnifiedOrderToLimeTrayOrder transforms unified order to LimeTray order format
func (t *transformersImpl) TransformUnifiedOrderToLimeTrayOrder(order *orders.Order) interface{} {
	// Transform to LimeTray order structure using proper structs
	return transformToLimeTrayOrderStruct(order)
}

// transformToLimeTrayOrderStruct transforms unified order to LimeTray order structure using proper structs
func transformToLimeTrayOrderStruct(order *orders.Order) interface{} {
	// Use constants for payment and delivery types
	serviceType := transformDeliveryModeToLimeTrayServiceType(order.OrderInfo.DeliveryMode)
	paymentMode := transformPaymentModeToLimeTray(order.Payment.Mode)

	// Transform customer information
	customer := limetray.Customer{
		Name:  order.Customer.FirstName + " " + order.Customer.LastName,
		Phone: order.Customer.Phone,
		Email: order.Customer.Email,
		Address: limetray.Address{
			Line1:     order.Customer.Address,
			City:      "Unknown", // Default value as not provided in unified structure
			State:     "Unknown", // Default value as not provided in unified structure
			Pincode:   "000000",  // Default value as not provided in unified structure
			Latitude:  0.0,       // Default value as not provided in unified structure
			Longitude: 0.0,       // Default value as not provided in unified structure
		},
	}

	// Transform order items
	items := make([]limetray.OrderItem, 0)
	for _, item := range order.Item {
		limeTrayItem := limetray.OrderItem{
			ProductID:    item.ItemID,
			ProductSkuID: item.ItemID, // Using same ID for SKU
			Name:         item.Name,
			Quantity:     item.Quantity,
			Price:        item.UnitPrice,
			Total:        item.UnitPrice * float64(item.Quantity),
			Addons:       transformOrderAddOns(item.AddOns),
			Taxes:        transformOrderTaxes(item.Taxes),
			Charges:      transformOrderCharges(item.Charges),
		}
		items = append(items, limeTrayItem)
	}

	// Transform payment information
	payment := limetray.Payment{
		Mode:          paymentMode,
		Status:        order.Payment.Status,
		Amount:        order.Payment.AmountPaid,
		TransactionID: "", // Not provided in unified structure
	}

	// Transform order info
	orderInfo := limetray.OrderInfo{
		OrderDate:       "", // Would need to convert from Unix timestamp
		DeliveryDate:    "", // Not provided in unified structure
		DeliveryTime:    "", // Not provided in unified structure
		Instructions:    order.OrderInfo.Instruction,
		SubTotal:        order.OrderInfo.SubTotal,
		TotalTax:        order.OrderInfo.TotalTaxes,
		TotalCharges:    0.0, // Would need to calculate from items
		TotalDiscount:   0.0, // Not provided in unified structure
		GrandTotal:      order.OrderInfo.Total,
		PreparationTime: 0, // Not provided in unified structure
	}

	limeTrayOrder := limetray.SaveOrderRequest{
		OrderID:      order.OrderInfo.OrderID,
		RestaurantID: order.OrderInfo.RestID,
		Customer:     customer,
		Items:        items,
		Payment:      payment,
		OrderInfo:    orderInfo,
		ServiceType:  serviceType,
	}

	return limeTrayOrder
}

// Helper functions for order transformation
func transformDeliveryModeToLimeTrayServiceType(deliveryMode string) string {
	switch strings.ToLower(deliveryMode) {
	case constants.DeliveryTypeDelivery:
		return constants.LimeTrayServiceTypeDelivery
	case constants.DeliveryTypeTakeaway:
		return constants.LimeTrayServiceTypeTakeaway
	case constants.DeliveryTypeDineIn:
		return constants.LimeTrayServiceTypeDineIn
	default:
		return constants.LimeTrayServiceTypeDelivery // Default to delivery
	}
}

func transformPaymentModeToLimeTray(mode string) string {
	switch strings.ToLower(mode) {
	case constants.PaymentTypeCash:
		return constants.LimeTrayPaymentModeCash
	case constants.PaymentTypeCard:
		return constants.LimeTrayPaymentModeCard
	case constants.PaymentTypeOnline, constants.PaymentTypeUPI:
		return constants.LimeTrayPaymentModeOnline
	default:
		return constants.LimeTrayPaymentModeOnline
	}
}

func transformOrderAddOns(addOns []orders.OrderAddOn) []limetray.OrderAddon {
	if len(addOns) == 0 {
		return nil
	}

	limeTrayAddOns := make([]limetray.OrderAddon, 0)
	for _, addon := range addOns {
		limeTrayAddon := limetray.OrderAddon{
			ProductID:    addon.ID,
			ProductSkuID: addon.ID, // Using same ID for SKU
			Name:         addon.Name,
			Price:        addon.UnitPrice,
			Quantity:     addon.Quantity,
		}
		limeTrayAddOns = append(limeTrayAddOns, limeTrayAddon)
	}

	return limeTrayAddOns
}

func transformOrderTaxes(taxes []orders.OrderTax) []limetray.OrderTax {
	if len(taxes) == 0 {
		return nil
	}

	limeTrayTaxes := make([]limetray.OrderTax, 0)
	for _, tax := range taxes {
		// Determine tax type based on value
		taxType := constants.LimeTrayTaxTypePercentage // Default to percentage
		if tax.Value < 1.0 {
			taxType = constants.LimeTrayTaxTypePercentage
		} else {
			taxType = constants.LimeTrayTaxTypeFlat
		}

		limeTrayTax := limetray.OrderTax{
			TaxID:  tax.Title, // Using title as ID
			Name:   tax.Title,
			Value:  tax.Value,
			Type:   taxType,
			Amount: tax.Value, // Assuming amount equals value for simplicity
		}
		limeTrayTaxes = append(limeTrayTaxes, limeTrayTax)
	}

	return limeTrayTaxes
}

func transformOrderCharges(charges []orders.OrderCharge) []limetray.OrderCharge {
	if len(charges) == 0 {
		return nil
	}

	limeTrayCharges := make([]limetray.OrderCharge, 0)
	for _, charge := range charges {
		// Determine charge type based on value
		chargeType := constants.LimeTrayTaxTypePercentage // Default to percentage
		if charge.Value < 1.0 {
			chargeType = constants.LimeTrayTaxTypePercentage
		} else {
			chargeType = constants.LimeTrayTaxTypeFlat
		}

		limeTrayCharge := limetray.OrderCharge{
			ChargeID: charge.Title, // Using title as ID
			Name:     charge.Title,
			Value:    charge.Value,
			Type:     chargeType,
			Amount:   charge.Value, // Assuming amount equals value for simplicity
		}
		limeTrayCharges = append(limeTrayCharges, limeTrayCharge)
	}

	return limeTrayCharges
}
