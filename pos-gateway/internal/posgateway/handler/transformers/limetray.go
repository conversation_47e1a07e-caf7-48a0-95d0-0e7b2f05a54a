package transformers

import (
	"strconv"
	"strings"

	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/limetray"
	"github.com/nutanalabs/pos-gateway/internal/utils"
	logger "github.com/roppenlabs/rapido-logger-go"
)

// TransformLimeTrayMenu converts LimeTray menu format to common format
func (t *transformersImpl) TransformLimeTrayMenu(menu *map[string]interface{}) *common.UnifiedMenu {
	var limeTrayMenu limetray.Menu
	err := utils.UnmarshalJSONToInterface(menu, &limeTrayMenu)
	if err != nil {
		logger.Error(logger.Format{
			Message: "Error while unmarshalling LimeTray menu",
			Data: map[string]string{
				"error": utils.ConvertInterfaceToJSON(err),
			},
		})
		return nil
	}

	if len(limeTrayMenu.Categories) == 0 {
		return nil
	}

	// Transform categories
	categories := transformLimeTrayCategories(limeTrayMenu.Categories)

	// Transform taxes
	taxes := transformLimeTrayTaxes(limeTrayMenu.Taxes)

	// Transform charges
	charges := transformLimeTrayCharges(limeTrayMenu.Charges)

	// Collect all addon groups from categories and items
	addonGroupsMap := make(map[string]limetray.ProductAddonTemplate)
	collectLimeTrayAddonGroups(limeTrayMenu.Categories, addonGroupsMap)

	// Transform addon groups to root level
	addonGroups := transformLimeTrayAddOnGroupsToRoot(addonGroupsMap)

	unifiedMenu := &common.UnifiedMenu{
		Categories:    categories,
		AddOnGroups:   addonGroups,
		VariantGroups: []common.VariantGroup{}, // LimeTray uses SKUs instead of variants
		Variants:      []common.Variant{},      // LimeTray uses SKUs instead of variants
		BillComponents: &common.BillComponents{
			Charges: charges,
			Taxes:   taxes,
		},
	}

	return unifiedMenu
}

// transformLimeTrayCategories converts LimeTray categories to common format
func transformLimeTrayCategories(categories []limetray.MenuCategory) []common.Category {
	if len(categories) == 0 {
		return nil
	}

	commonCategories := make([]common.Category, 0, len(categories))
	for _, category := range categories {
		providerId := strconv.Itoa(category.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonCategory := common.Category{
			ID:            &id,
			ProviderId:    &providerId,
			Name:          &category.CategoryName,
			Description:   category.CategoryDescription,
			ImageURL:      getFirstImageURL(category.CategoryImageList),
			SortOrder:     category.Rank,
			Items:         transformLimeTrayItems(category.ProductList),
			Subcategories: transformLimeTraySubcategories(category.ChildCategories),
		}
		commonCategories = append(commonCategories, commonCategory)
	}

	return commonCategories
}

// transformLimeTraySubcategories converts LimeTray subcategories to common format
func transformLimeTraySubcategories(subcategories []limetray.MenuCategory) []common.Category {
	if len(subcategories) == 0 {
		return nil
	}

	commonSubcategories := make([]common.Category, 0, len(subcategories))
	for _, subcategory := range subcategories {
		providerId := strconv.Itoa(subcategory.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonSubcategory := common.Category{
			ID:            &id,
			ProviderId:    &providerId,
			Name:          &subcategory.CategoryName,
			Description:   subcategory.CategoryDescription,
			ImageURL:      getFirstImageURL(subcategory.CategoryImageList),
			SortOrder:     subcategory.Rank,
			Items:         transformLimeTrayItems(subcategory.ProductList),
			Subcategories: transformLimeTraySubcategories(subcategory.ChildCategories),
		}
		commonSubcategories = append(commonSubcategories, commonSubcategory)
	}

	return commonSubcategories
}

// transformLimeTrayItems converts LimeTray products to common items
func transformLimeTrayItems(products []limetray.MenuProduct) []common.Item {
	if len(products) == 0 {
		return nil
	}

	commonItems := make([]common.Item, 0)
	for _, product := range products {
		if product.ProductID == nil {
			continue
		}

		// For each product, create items from its SKUs
		for _, sku := range product.ProductSkuList {
			if sku.ProductSkuID == nil {
				continue
			}

			providerId := strconv.Itoa(*sku.ProductSkuID)
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			// Transform food type
			foodType := "not Specified" // Default value
			if product.ProductType != nil {
				if internalType, ok := constants.LimeTrayFoodTypesMapToInternal[*product.ProductType]; ok {
					foodType = internalType
				}
			}

			// Get price from SKU
			var price *float64
			if sku.ProductSkuPrice != nil {
				price = sku.ProductSkuPrice
			}

			// Transform addon group IDs
			addOnGroupIDs := make([]string, 0)
			for _, addon := range sku.Addons {
				if addon.CategoryID != nil {
					addonProviderId := strconv.Itoa(*addon.CategoryID)
					addonId := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + addonProviderId
					addOnGroupIDs = append(addOnGroupIDs, addonId)
				}
			}

			// Get item name (prefer SKU name, fallback to product name)
			var itemName *string
			if sku.ProductSkuName != nil && *sku.ProductSkuName != "" {
				itemName = sku.ProductSkuName
			} else if product.ProductName != nil {
				itemName = product.ProductName
			}

			// Get item description (prefer SKU description, fallback to product description)
			var itemDescription *string
			if sku.ProductSkuDescription != nil && *sku.ProductSkuDescription != "" {
				itemDescription = sku.ProductSkuDescription
			} else if product.ProductDescription != nil {
				itemDescription = product.ProductDescription
			}

			// Get image URL (prefer SKU images, fallback to product images)
			var imageURL *string
			if len(sku.ProductSkuImageList) > 0 {
				imageURL = &sku.ProductSkuImageList[0]
			} else if len(product.ProductImageList) > 0 {
				imageURL = &product.ProductImageList[0]
			}

			commonItem := common.Item{
				ID:            &id,
				ProviderId:    &providerId,
				Name:          itemName,
				Description:   itemDescription,
				ImageURL:      imageURL,
				Price:         price,
				InStock:       &[]bool{!sku.OutOfStock}[0],
				FoodType:      &foodType,
				AddOnGroupIDs: addOnGroupIDs,
				SortOrder:     getProductRank(product.Rank),
			}

			commonItems = append(commonItems, commonItem)
		}
	}

	return commonItems
}

// Helper functions
func transformLimeTrayCategoryTypeToFoodType(categoryType string) string {
	switch strings.ToUpper(categoryType) {
	case constants.LimeTrayCategoryTypeVeg:
		return "veg"
	case constants.LimeTrayCategoryTypeNonVeg:
		return "non veg"
	default:
		return "not Specified"
	}
}

func getFirstImageURL(images []string) *string {
	if len(images) > 0 {
		return &images[0]
	}
	return nil
}

func getProductRank(rank *int) int {
	if rank != nil {
		return *rank
	}
	return 0
}

// transformLimeTrayTaxes converts LimeTray taxes to common format
func transformLimeTrayTaxes(taxes []limetray.ProductTaxCharge) []common.Tax {
	if len(taxes) == 0 {
		return nil
	}

	commonTaxes := make([]common.Tax, 0)
	for _, tax := range taxes {
		providerId := tax.TaxChargeID
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonTax := common.Tax{
			ID:         &id,
			ProviderId: &providerId,
			Name:       &tax.TaxChargeName,
			Value:      &tax.TaxValue,
		}
		commonTaxes = append(commonTaxes, commonTax)
	}

	return commonTaxes
}

// transformLimeTrayCharges converts LimeTray charges to common format
func transformLimeTrayCharges(charges []limetray.ProductTaxCharge) []common.Charge {
	if len(charges) == 0 {
		return nil
	}

	commonCharges := make([]common.Charge, 0)
	for _, charge := range charges {
		providerId := charge.TaxChargeID
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		// Transform charge type
		var chargeType string
		if charge.TaxType == constants.LimeTrayTaxTypeFlat {
			chargeType = "FIXED"
		} else {
			chargeType = "PERCENTAGE"
		}

		commonCharge := common.Charge{
			ID:         &id,
			ProviderId: &providerId,
			Name:       &charge.TaxChargeName,
			Value:      &charge.TaxValue,
			Type:       &chargeType,
		}
		commonCharges = append(commonCharges, commonCharge)
	}

	return commonCharges
}

// collectLimeTrayAddonGroups recursively collects all addon groups from categories and items
func collectLimeTrayAddonGroups(categories []limetray.MenuCategory, addonGroupsMap map[string]limetray.ProductAddonTemplate) {
	for _, category := range categories {
		// Process items in current category
		for _, product := range category.ProductList {
			// Process SKUs in product
			for _, sku := range product.ProductSkuList {
				// Collect addon groups from SKU
				for _, addon := range sku.Addons {
					if addon.CategoryID != nil {
						addonKey := strconv.Itoa(*addon.CategoryID)
						addonGroupsMap[addonKey] = addon
					}
				}
			}
		}

		// Recursively process subcategories
		collectLimeTrayAddonGroups(category.ChildCategories, addonGroupsMap)
	}
}

// transformLimeTrayAddOnGroupsToRoot transforms addon groups to root level
func transformLimeTrayAddOnGroupsToRoot(groupsMap map[string]limetray.ProductAddonTemplate) []common.AddOnGroup {
	commonGroups := make([]common.AddOnGroup, 0)

	for _, group := range groupsMap {
		if group.CategoryID == nil {
			continue
		}

		providerId := strconv.Itoa(*group.CategoryID)
		id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

		commonGroup := common.AddOnGroup{
			ID:             &id,
			ProviderId:     &providerId,
			Name:           group.CategoryName,
			MinimumNeeded:  group.Min,
			MaximumAllowed: group.Max,
			AddOns:         transformLimeTrayAddOns(group.ProductList),
			SortOrder:      group.Rank,
		}
		commonGroups = append(commonGroups, commonGroup)
	}

	return commonGroups
}

// transformLimeTrayAddOns converts LimeTray addon products to common addons
func transformLimeTrayAddOns(products []limetray.MenuProduct) []common.AddOn {
	if len(products) == 0 {
		return nil
	}

	commonAddOns := make([]common.AddOn, 0)
	for _, product := range products {
		if product.ProductID == nil {
			continue
		}

		// For each product, create addons from its SKUs
		for _, sku := range product.ProductSkuList {
			if sku.ProductSkuID == nil {
				continue
			}

			providerId := strconv.Itoa(*sku.ProductSkuID)
			id := constants.ProviderAbbreviationMap[constants.LimeTrayProvider] + providerId

			// Get addon name (prefer SKU name, fallback to product name)
			var addonName *string
			if sku.ProductSkuName != nil && *sku.ProductSkuName != "" {
				addonName = sku.ProductSkuName
			} else if product.ProductName != nil {
				addonName = product.ProductName
			}

			// Get price from SKU
			var price *float64
			if sku.ProductSkuPrice != nil {
				price = sku.ProductSkuPrice
			}

			commonAddOn := common.AddOn{
				ID:         &id,
				ProviderId: &providerId,
				Name:       addonName,
				Price:      price,
				InStock:    &[]bool{!sku.OutOfStock}[0],
				SortOrder:  getProductRank(product.Rank),
			}

			commonAddOns = append(commonAddOns, commonAddOn)
		}
	}

	return commonAddOns
}
